{"name": "bridge-me", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "classnames": "^2.5.1", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.513.0", "mongodb": "^6.10.0", "mongoose": "^8.7.3", "next": "^14.2.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-leaflet": "^4.2.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/leaflet": "^1.9.18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^14.2.3", "jest": "^29.7.0", "typescript": "^5"}, "description": "This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).", "main": "index.js", "author": "", "license": "ISC"}