.container {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-6);
  background: rgba(250, 255, 202, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--accent-300);
  position: sticky;
  top: 0;
  z-index: 50;
  transition: all var(--transition-medium);
}

.container:hover {
  background: rgba(250, 255, 202, 0.98);
  box-shadow: var(--shadow-accent);
  transform: translateY(-1px);
}

.logo {
  display: flex;
  align-items: center;
  width: 120px;
  height: 40px;
  position: relative;
  transition: all var(--transition-medium);
}

.logo:hover {
  transform: scale(1.05) translateY(-2px);
}

.image {
  object-fit: contain;
  width: 100% !important;
  height: 100% !important;
}
