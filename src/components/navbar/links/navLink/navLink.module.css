.container {
  position: relative;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  font-size: var(--font-size-sm);
  text-align: center;
  color: var(--text-secondary);
  transition: var(--transition-fast);
  text-decoration: none;
  display: inline-block;
}

.container:hover {
  color: var(--primary-600);
  background: var(--primary-50);
  transform: translateY(-1px);
}

.active {
  background: var(--primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.active:hover {
  background: var(--primary-700);
  color: white;
}
