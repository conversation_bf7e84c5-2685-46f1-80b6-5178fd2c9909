.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-medium);
  font-size: var(--font-size-base);
  line-height: 1;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Primary Button */
.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-primary);
  border-radius: var(--radius-xl);
}

.primary:hover:not(.disabled) {
  box-shadow: var(--shadow-xl);
  transform: translateY(-3px) scale(1.02);
  filter: brightness(1.1);
}

.primary:active:not(.disabled) {
  transform: translateY(-1px) scale(1.01);
}

/* Secondary Button */
.secondary {
  background: var(--gradient-glass);
  backdrop-filter: blur(10px);
  color: var(--primary-700);
  border: 2px solid var(--primary-700);
  border-radius: var(--radius-xl);
}

.secondary:hover:not(.disabled) {
  background: var(--gradient-primary);
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-primary);
}

.secondary:active:not(.disabled) {
  transform: translateY(-1px) scale(1.01);
}

/* Warning Button */
.warning {
  background-color: var(--warning-500);
  color: white;
  box-shadow: var(--shadow-sm);
}

.warning:hover:not(.disabled) {
  background-color: #d97706;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Button Sizes */
.small {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

.big {
  padding: var(--space-5) var(--space-10);
  font-size: var(--font-size-lg);
  min-width: 200px;
  font-weight: var(--font-bold);
  border-radius: var(--radius-2xl);
}

/* Disabled State */
.disabled {
  background-color: var(--neutral-300) !important;
  color: var(--neutral-500) !important;
  border-color: var(--neutral-300) !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  transform: none !important;
}

/* Loading Animation */
.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: var(--transition-slow);
}

.button:hover::before:not(.disabled) {
  left: 100%;
}

.button:focus {
  outline: none;
  border: none;
}

/* Active state */
.button:active {
  transform: scale(0.98);
}
