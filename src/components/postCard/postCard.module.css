.container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}
.top {
  display: flex;
}

.imgContainer {
  width: 90%;
  height: 400px;
  position: relative;
}
.img {
  object-fit: cover;
}

.date {
  font-size: 12px;
  transform: rotate(270deg);
  margin: auto;
}

.title {
  width: 90%;
  font-size: 24px;
  margin-bottom: 20px;
}
.desc {
  width: 90%;
  margin-bottom: 20px;
  font-weight: 300;
  color: gray;
}

.link {
  text-decoration: underline;
}
