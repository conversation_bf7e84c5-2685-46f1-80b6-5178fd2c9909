/* Hero Section */
.hero {
  min-height: 90vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--light-400) 0%, var(--accent-400) 50%, var(--secondary-500) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-300) 0%, transparent 100%);
  opacity: 0.3;
  z-index: 1;
}

.heroContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  position: relative;
  z-index: 2;
}

.heroText {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.heroTitle {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.highlight {
  color: var(--primary-700);
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--secondary-500), var(--primary-700));
  border-radius: var(--radius-full);
}

.heroSubtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-8);
}

.searchSection {
  margin: var(--space-8) 0;
}

.heroButtons {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.heroImage {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-700), var(--secondary-500));
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.floatingCard {
  position: absolute;
  top: var(--space-8);
  right: var(--space-8);
  background: white;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  animation: float 3s ease-in-out infinite;
}

.cardIcon {
  color: var(--primary-700);
  font-size: var(--font-size-lg);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Stats Section */
.statsSection {
  padding: var(--space-16) 0;
  background: linear-gradient(135deg, var(--light-200) 0%, var(--accent-200) 100%);
  width: 100%;
  position: relative;
  overflow: hidden;
}



.statsGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-6);
  position: relative;
  z-index: 1;
}

.statCard {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-glass);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-natural);
}

.statCard:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow: var(--shadow-xl);
  background: rgba(255, 255, 255, 0.4);
}

.statIcon {
  color: var(--primary-600);
  margin-bottom: var(--space-4);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  margin: 0 auto var(--space-4);
  color: white;
  box-shadow: var(--shadow-primary);
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.statLabel {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Features Section */
.featuresSection {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, var(--light-200) 0%, var(--accent-200) 100%);
  width: 100%;
}

.sectionHeader {
  text-align: center;
  margin-bottom: var(--space-20);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.sectionHeader h2 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-black);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionHeader p {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.featuresGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-10);
}

.featureCard {
  padding: var(--space-10);
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
  transition: all var(--transition-medium);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-natural);
  transform: scaleX(0);
  transition: transform var(--transition-medium);
}

.featureCard:hover::before {
  transform: scaleX(1);
}

.featureCard:hover {
  transform: translateY(-8px) scale(1.01);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-200);
}

.featureIcon {
  margin-bottom: var(--space-8);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  margin: 0 auto var(--space-8);
  color: white;
  box-shadow: var(--shadow-primary);
  transition: all var(--transition-medium);
}

.featureCard:hover .featureIcon {
  transform: scale(1.05) rotate(2deg);
  box-shadow: var(--shadow-lg);
}

.featureCard h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.featureCard p {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-8);
  font-size: var(--font-size-base);
}

.featureLink {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-600);
  font-weight: var(--font-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all var(--transition-fast);
  padding: var(--space-3) var(--space-6);
  border: 2px solid var(--primary-200);
  border-radius: var(--radius-lg);
  background: var(--primary-50);
}

.featureLink:hover {
  color: white;
  background: var(--primary-600);
  border-color: var(--primary-600);
  gap: var(--space-3);
  transform: translateY(-2px);
}

/* Results Section */
.resultsSection {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, var(--secondary-100) 0%, var(--primary-100) 100%);
  width: 100%;
}

.resultsSection h2 {
  text-align: center;
  margin-bottom: var(--space-12);
  font-size: var(--font-size-3xl);
  color: var(--text-primary);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: var(--space-12);
  padding: 0 var(--space-6);
}

.resultsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.resultCard {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--border-light);
}

.resultCard:hover {
  transform: translateY(-3px) scale(1.005);
  box-shadow: var(--shadow-lg);
}

.resultImageWrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.resultImage {
  object-fit: cover;
  transition: var(--transition-slow);
}

.resultCard:hover .resultImage {
  transform: scale(1.03);
}

.resultContent {
  padding: var(--space-6);
}

.resultContent h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.resultDescription {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-4);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.resultMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.provider {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.price {
  font-size: var(--font-size-lg);
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .heroContent {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }

  .heroTitle {
    font-size: var(--font-size-4xl);
  }

  .heroImage {
    height: 400px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 80vh;
    padding: var(--space-8) 0;
  }

  .heroTitle {
    font-size: var(--font-size-3xl);
  }

  .heroSubtitle {
    font-size: var(--font-size-lg);
  }

  .heroButtons {
    flex-direction: column;
    align-items: center;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .featuresGrid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .resultsGrid {
    grid-template-columns: 1fr;
  }

  .sectionHeader h2 {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: var(--font-size-2xl);
  }

  .heroSubtitle {
    font-size: var(--font-size-base);
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .statCard {
    padding: var(--space-4);
  }

  .statIcon {
    width: 40px;
    height: 40px;
  }

  .statValue {
    font-size: var(--font-size-xl);
  }

  .heroImage {
    height: 300px;
  }
}
