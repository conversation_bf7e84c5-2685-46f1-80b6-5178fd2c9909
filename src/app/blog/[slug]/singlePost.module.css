.container {
  display: flex;
  gap: 100px;
}
.imgContainer {
  flex: 1;
  position: relative;
  height: calc(100vh - 200px);
}

.img {
  object-fit: cover;
}

.textContainer {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.title {
  font-size: 44px;
}

.detail {
  display: flex;
  gap: 10px;
}

.avatar {
  object-fit: cover;
  border-radius: 50%;
}

.detailText {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detailTitle {
  color: gray;
  font-weight: bold;
}

.detailValue {
  font-weight: 500;
}

.content {
  font-size: 20px;
  color: gray;
}

@media (max-width: 768px) {
  .imgContainer {
    display: none;
  }
}
