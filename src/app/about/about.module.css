.container {
  display: flex;
  gap: 100px;
}
.textContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 50px;
}

.imgContainer {
  flex: 1;
  display: flex;
  flex-direction: column-reverse;
  gap: 50px;
  position: relative;
}

.img {
  object-fit: contain;
}

.subtitle {
  color: var(--btn);
}

.desc {
  font-weight: 300;
  text-align: justify;
}
.boxes {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.box {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.box h2 {
  color: var(--btn);
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
    text-align: center;
  }
  .boxes {
    flex-direction: column;
    gap: 50px;
  }
}
