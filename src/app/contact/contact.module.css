.container {
  display: flex;
  align-items: center;
  gap: 100px;
}

.imgContainer {
  flex: 1;
  position: relative;
  height: 500px;
}

.img {
  object-fit: contain;
}
.formContainer {
  flex: 1;
}
.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form input,
.form textarea {
  padding: 20px;
  border-radius: 5px;
  border: none;
  outline: none;
  background-color: var(--bgSoft);
  color: var(--text);
}
.form button {
  padding: 20px;
  background-color: var(--btn);
  color: var(--text);
  border: none;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
}
